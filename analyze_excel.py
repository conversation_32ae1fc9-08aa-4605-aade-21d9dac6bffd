#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
import os

def read_title_file_simple():
    """简单读取title.xlsx文件"""
    try:
        # 尝试读取CSV格式（如果存在）
        if os.path.exists("title.csv"):
            df = pd.read_csv("title.csv")
            print("从CSV文件读取成功")
            return df

        # 尝试不同的方法读取Excel
        for engine in ['openpyxl', 'xlrd']:
            try:
                df = pd.read_excel("title.xlsx", engine=engine)
                print(f"使用{engine}引擎读取成功")
                return df
            except Exception as e:
                print(f"使用{engine}引擎失败: {e}")
                continue

        # 如果都失败，尝试读取第一个工作表
        try:
            xl_file = pd.ExcelFile("title.xlsx")
            print(f"工作表: {xl_file.sheet_names}")
            df = pd.read_excel("title.xlsx", sheet_name=0)
            return df
        except Exception as e:
            print(f"读取第一个工作表失败: {e}")
            return None

    except Exception as e:
        print(f"读取title.xlsx失败: {e}")
        return None

def analyze_excel_files():
    """分析两个Excel文件的结构"""
    
    # 文件路径
    title_file = "title.xlsx"
    category_file = "分类合集.xlsx"
    
    print("=== 分析 title.xlsx 文件结构 ===")
    try:
        # 尝试不同的方法读取title.xlsx
        try:
            title_df = pd.read_excel(title_file, engine='openpyxl')
        except:
            try:
                title_df = pd.read_excel(title_file, engine='xlrd')
            except:
                # 尝试读取所有工作表
                xl_file = pd.ExcelFile(title_file)
                print(f"工作表名称: {xl_file.sheet_names}")
                title_df = pd.read_excel(title_file, sheet_name=xl_file.sheet_names[0])

        print(f"行数: {len(title_df)}")
        print(f"列数: {len(title_df.columns)}")
        print(f"列名: {list(title_df.columns)}")
        print("\n前5行数据:")
        print(title_df.head())
        print("\n数据类型:")
        print(title_df.dtypes)

        # 保存为CSV以便查看
        title_df.to_csv("title_analysis.csv", index=False, encoding='utf-8-sig')
        print(f"\n已保存分析结果到 title_analysis.csv")

    except Exception as e:
        print(f"读取 title.xlsx 时出错: {e}")
        # 尝试用xlrd读取
        try:
            import xlrd
            workbook = xlrd.open_workbook(title_file)
            print(f"工作表数量: {workbook.nsheets}")
            print(f"工作表名称: {workbook.sheet_names()}")
            sheet = workbook.sheet_by_index(0)
            print(f"行数: {sheet.nrows}, 列数: {sheet.ncols}")
            if sheet.nrows > 0:
                print("第一行数据:", [sheet.cell_value(0, col) for col in range(sheet.ncols)])
        except Exception as e2:
            print(f"使用xlrd也失败: {e2}")
    
    print("\n" + "="*50)
    print("=== 分析 分类合集.xlsx 文件结构 ===")
    try:
        # 读取分类合集.xlsx
        category_df = pd.read_excel(category_file)
        print(f"行数: {len(category_df)}")
        print(f"列数: {len(category_df.columns)}")
        print(f"列名: {list(category_df.columns)}")
        print("\n前5行数据:")
        print(category_df.head())
        print("\n数据类型:")
        print(category_df.dtypes)
        
        # 保存为CSV以便查看
        category_df.to_csv("category_analysis.csv", index=False, encoding='utf-8-sig')
        print(f"\n已保存分析结果到 category_analysis.csv")
        
    except Exception as e:
        print(f"读取 分类合集.xlsx 时出错: {e}")

if __name__ == "__main__":
    # 先尝试简单读取title文件
    print("=== 尝试读取 title.xlsx ===")
    title_df = read_title_file_simple()
    if title_df is not None:
        print(f"成功读取title.xlsx: {len(title_df)}行, {len(title_df.columns)}列")
        print(f"列名: {list(title_df.columns)}")
        print("前5行:")
        print(title_df.head())
        title_df.to_csv("title_simple.csv", index=False, encoding='utf-8-sig')
        print("已保存到 title_simple.csv")
    else:
        print("无法读取title.xlsx")

    print("\n" + "="*50)
    analyze_excel_files()

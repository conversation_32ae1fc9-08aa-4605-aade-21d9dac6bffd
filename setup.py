#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys
import os

def install_requirements():
    """安装必要的依赖包"""
    requirements = [
        "pandas",
        "streamlit", 
        "openpyxl",
        "xlrd"
    ]
    
    print("🔧 正在安装依赖包...")
    for package in requirements:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")
            return False
    
    return True

def check_files():
    """检查必要的文件是否存在"""
    required_files = [
        "分类合集.xlsx",
        "game_auto_fill_tool.py",
        "web_interface.py",
        "cli_tool.py"
    ]
    
    print("\n📁 检查文件...")
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 文件不存在")
            missing_files.append(file)
    
    return len(missing_files) == 0

def setup_data():
    """设置数据文件"""
    print("\n📊 设置数据文件...")
    
    # 检查是否需要转换Excel文件
    if not os.path.exists("category_analysis.csv"):
        if os.path.exists("分类合集.xlsx"):
            try:
                import pandas as pd
                df = pd.read_excel("分类合集.xlsx")
                df.to_csv("category_analysis.csv", index=False, encoding='utf-8-sig')
                print("✅ 分类合集数据转换完成")
            except Exception as e:
                print(f"❌ 转换分类合集数据失败: {e}")
                return False
        else:
            print("❌ 未找到分类合集.xlsx文件")
            return False
    else:
        print("✅ 分类合集数据已存在")
    
    # 尝试转换title.xlsx
    if os.path.exists("title.xlsx") and not os.path.exists("title_converted.csv"):
        try:
            import pandas as pd
            df = pd.read_excel("title.xlsx")
            df.to_csv("title_converted.csv", index=False, encoding='utf-8-sig')
            print("✅ Title数据转换完成")
        except Exception as e:
            print(f"⚠️ Title数据转换失败，将使用分类合集数据: {e}")
    
    return True

def main():
    """主安装函数"""
    print("🎮 游戏信息自动填写工具 - 安装程序")
    print("=" * 50)
    
    # 1. 安装依赖
    if not install_requirements():
        print("\n❌ 依赖安装失败，请手动安装")
        return False
    
    # 2. 检查文件
    if not check_files():
        print("\n❌ 缺少必要文件，请确保所有文件都在当前目录")
        return False
    
    # 3. 设置数据
    if not setup_data():
        print("\n❌ 数据设置失败")
        return False
    
    print("\n🎉 安装完成！")
    print("\n📖 使用方法:")
    print("1. Web界面: streamlit run web_interface.py")
    print("2. 命令行: python3 cli_tool.py --name '游戏名' --link '链接'")
    print("3. Python脚本: from game_auto_fill_tool import GameAutoFillTool")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

import openpyxl
import csv

# 读取Excel文件
def excel_to_csv(excel_path, csv_path):
    wb = openpyxl.load_workbook(excel_path)
    ws = wb.active
    with open(csv_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        # 写入表头
        for row in ws.iter_rows(min_row=1, max_row=1, values_only=True):
            writer.writerow(row)
        # 写入内容
        for row in ws.iter_rows(min_row=2, values_only=True):
            writer.writerow(row)

if __name__ == '__main__':
    excel_to_csv('title.xlsx', 'title_dict.csv') 
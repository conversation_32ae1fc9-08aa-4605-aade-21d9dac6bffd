#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re
from datetime import datetime
import os
from difflib import SequenceMatcher

# OpenAI是可选的，如果需要AI生成介绍可以安装
try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False

class GameAutoFillTool:
    def __init__(self):
        """初始化游戏自动填写工具"""
        self.category_df = None
        self.title_dict = {}
        self.load_data()
        
        # 设置OpenAI API（如果需要AI生成游戏介绍）
        # openai.api_key = "your-api-key-here"  # 请替换为您的API密钥
        
    def load_data(self):
        """加载现有数据"""
        try:
            # 加载分类合集数据
            self.category_df = pd.read_csv("category_analysis.csv")
            print(f"✓ 成功加载分类合集数据: {len(self.category_df)} 条记录")
            
            # 尝试加载title字典数据（如果存在）
            if os.path.exists("title_converted.csv"):
                title_df = pd.read_csv("title_converted.csv")
                # 构建title字典，用于快速匹配
                for _, row in title_df.iterrows():
                    if pd.notna(row.get('Title ID')) and pd.notna(row.get('游戏中文名称')):
                        self.title_dict[row['游戏中文名称']] = row['Title ID']
                print(f"✓ 成功加载title字典: {len(self.title_dict)} 条记录")
            else:
                print("⚠ 未找到title字典文件，将基于现有数据构建")
                self._build_title_dict_from_category()
                
        except Exception as e:
            print(f"✗ 加载数据时出错: {e}")
            
    def _build_title_dict_from_category(self):
        """从分类合集数据构建title字典"""
        if self.category_df is not None:
            for _, row in self.category_df.iterrows():
                if pd.notna(row.get('Title ID')) and pd.notna(row.get('游戏中文名称')):
                    self.title_dict[row['游戏中文名称']] = row['Title ID']
            print(f"✓ 从分类合集构建title字典: {len(self.title_dict)} 条记录")
    
    def fuzzy_match_game(self, game_name, threshold=0.6):
        """模糊匹配游戏名称"""
        best_match = None
        best_score = 0
        
        for existing_name in self.title_dict.keys():
            # 计算相似度
            score = SequenceMatcher(None, game_name.lower(), existing_name.lower()).ratio()
            if score > best_score and score >= threshold:
                best_score = score
                best_match = existing_name
                
        return best_match, best_score
    
    def extract_title_id_from_link(self, game_link):
        """从游戏链接中提取Title ID"""
        if not game_link:
            return None
            
        # 常见的Title ID格式匹配
        patterns = [
            r'([0-9A-F]{16})',  # 16位十六进制
            r'([0-9A-F]{12})',  # 12位十六进制
            r'/([0-9A-F]+)/',   # 路径中的十六进制
        ]
        
        for pattern in patterns:
            match = re.search(pattern, game_link.upper())
            if match:
                return match.group(1)
        
        return None
    
    def generate_game_description(self, game_name, game_category="动作冒险"):
        """生成游戏介绍（使用AI或模板）"""
        # 这里可以集成OpenAI API来生成更智能的介绍
        # 现在使用模板生成
        
        templates = {
            "动作冒险": f"《{game_name}》是一款充满刺激的动作冒险游戏！在这个奇幻的世界中，你将扮演勇敢的冒险者，探索未知的领域，挑战强大的敌人。独特的核心玩法和精美的视觉风格，将带给你前所未有的游戏体验！快来加入这场惊心动魄的冒险之旅吧！",
            
            "角色扮演": f"踏入《{game_name}》的奇幻世界！这款深度角色扮演游戏将带你体验史诗般的冒险旅程。丰富的角色养成系统，精彩的剧情故事，让你沉浸在一个充满魔法与传奇的世界中。快来书写属于你的英雄传说！",
            
            "策略": f"《{game_name}》是一款考验智慧与策略的精品游戏！运用你的战术思维，合理布局，制定完美的作战计划。独特的策略玩法和丰富的战斗系统，将挑战你的极限思维。准备好成为最强的战略家了吗？",
            
            "体育竞技": f"感受《{game_name}》带来的竞技激情！这款体育游戏以其真实的物理引擎和流畅的操作体验，让你体验到最纯粹的竞技乐趣。挑战全球玩家，证明你的实力，成为竞技场上的传奇！",
            
            "模拟经营": f"欢迎来到《{game_name}》的经营世界！在这里，你将化身为精明的管理者，运用智慧和策略，打造属于你的商业帝国。丰富的经营玩法和真实的模拟体验，让你感受成功的喜悦！",
            
            "休闲益智": f"《{game_name}》为你带来轻松愉快的游戏时光！这款休闲益智游戏以其简单易懂的玩法和精美的画面设计，让你在放松的同时锻炼思维。快来享受这场智慧与乐趣并存的游戏体验吧！",
            
            "多人游戏": f"《{game_name}》邀请你加入多人竞技的世界！与全球玩家同场竞技，体验团队合作的乐趣。丰富的多人模式和公平的竞技环境，让你在激烈的对战中展现实力，结交志同道合的朋友！",
            
            "其他": f"《{game_name}》是一款独具特色的游戏作品！凭借其创新的玩法和独特的艺术风格，为玩家带来全新的游戏体验。精心设计的关卡和丰富的内容，让你在游戏中发现无限的乐趣和惊喜！"
        }
        
        return templates.get(game_category, templates["其他"])
    
    def generate_image_urls(self, title_id):
        """生成图片URL"""
        if not title_id:
            return None, None, None, None
            
        base_url = "https://switchpojie.pro/wp-content/uploads/2025/06/an"
        
        fengmian = f"{base_url}/{title_id}/banner/1.jpg"
        jietu1 = f"{base_url}/{title_id}/screenshots/1.jpg"
        jietu2 = f"{base_url}/{title_id}/screenshots/2.jpg"
        jietu3 = f"{base_url}/{title_id}/screenshots/3.jpg"
        
        return fengmian, jietu1, jietu2, jietu3
    
    def auto_fill_game_info(self, game_name, game_link, game_category="动作冒险"):
        """自动填写游戏信息"""
        result = {
            "游戏中文名称": game_name,
            "游戏链接": game_link,
            "游戏分类": game_category,
            "Title ID": None,
            "英文名": "",
            "发布日期": "",
            "地区": "US",
            "游戏介绍": "",
            "fengmian": "",
            "jietu1": "",
            "jietu2": "",
            "jietu3": "",
            "匹配信息": ""
        }
        
        # 1. 尝试精确匹配
        if game_name in self.title_dict:
            result["Title ID"] = self.title_dict[game_name]
            result["匹配信息"] = "精确匹配"
        else:
            # 2. 尝试模糊匹配
            match_name, score = self.fuzzy_match_game(game_name)
            if match_name:
                result["Title ID"] = self.title_dict[match_name]
                result["匹配信息"] = f"模糊匹配: {match_name} (相似度: {score:.2f})"
            else:
                # 3. 尝试从链接提取Title ID
                extracted_id = self.extract_title_id_from_link(game_link)
                if extracted_id:
                    result["Title ID"] = extracted_id
                    result["匹配信息"] = "从链接提取"
                else:
                    result["匹配信息"] = "未找到匹配"
        
        # 4. 如果找到了Title ID，查找更多信息
        if result["Title ID"] and self.category_df is not None:
            matching_rows = self.category_df[self.category_df['Title ID'] == result["Title ID"]]
            if not matching_rows.empty:
                row = matching_rows.iloc[0]
                result["英文名"] = row.get("英文名", "")
                result["发布日期"] = row.get("发布日期", "")
                result["地区"] = row.get("地区", "US")
                result["游戏分类"] = row.get("游戏分类", game_category)
        
        # 5. 生成游戏介绍
        result["游戏介绍"] = self.generate_game_description(game_name, result["游戏分类"])
        
        # 6. 生成图片URL
        if result["Title ID"]:
            fengmian, jietu1, jietu2, jietu3 = self.generate_image_urls(result["Title ID"])
            result["fengmian"] = fengmian
            result["jietu1"] = jietu1
            result["jietu2"] = jietu2
            result["jietu3"] = jietu3
        
        return result
    
    def batch_process(self, input_file, output_file):
        """批量处理游戏信息"""
        try:
            # 读取输入文件
            input_df = pd.read_csv(input_file)
            results = []
            
            for _, row in input_df.iterrows():
                game_name = row.get("游戏中文名称", "")
                game_link = row.get("游戏链接", "")
                game_category = row.get("游戏分类", "动作冒险")
                
                if game_name:
                    result = self.auto_fill_game_info(game_name, game_link, game_category)
                    results.append(result)
                    print(f"✓ 处理完成: {game_name} - {result['匹配信息']}")
            
            # 保存结果
            result_df = pd.DataFrame(results)
            result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"✓ 批量处理完成，结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"✗ 批量处理时出错: {e}")

def main():
    """主函数 - 演示用法"""
    tool = GameAutoFillTool()
    
    # 示例：单个游戏信息填写
    print("=== 单个游戏信息填写示例 ===")
    result = tool.auto_fill_game_info(
        game_name="塞尔达传说",
        game_link="https://example.com/game/010028600EBDA000",
        game_category="动作冒险"
    )
    
    print("填写结果:")
    for key, value in result.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    main()

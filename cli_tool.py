#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import sys
from game_auto_fill_tool import GameAutoFillTool

def main():
    parser = argparse.ArgumentParser(description="游戏信息自动填写工具")
    parser.add_argument("--name", "-n", help="游戏中文名称", required=True)
    parser.add_argument("--link", "-l", help="游戏链接", default="")
    parser.add_argument("--category", "-c", help="游戏分类", default="动作冒险")
    parser.add_argument("--output", "-o", help="输出文件名", default="output.csv")
    
    args = parser.parse_args()
    
    print("🎮 游戏信息自动填写工具")
    print("=" * 50)
    
    # 初始化工具
    print("正在加载数据...")
    tool = GameAutoFillTool()
    
    # 处理游戏信息
    print(f"正在处理: {args.name}")
    result = tool.auto_fill_game_info(args.name, args.link, args.category)
    
    # 显示结果
    print("\n📋 填写结果:")
    print("-" * 30)
    for key, value in result.items():
        if key != "游戏介绍":  # 游戏介绍太长，单独显示
            print(f"{key}: {value}")
    
    print(f"\n📝 游戏介绍:")
    print(result["游戏介绍"])
    
    # 保存结果
    import pandas as pd
    df = pd.DataFrame([result])
    df.to_csv(args.output, index=False, encoding='utf-8-sig')
    print(f"\n✅ 结果已保存到: {args.output}")

if __name__ == "__main__":
    main()

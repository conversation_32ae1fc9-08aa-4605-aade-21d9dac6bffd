#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import streamlit as st
import pandas as pd
from game_auto_fill_tool import GameAutoFillTool
import io

def main():
    st.set_page_config(
        page_title="游戏信息自动填写工具",
        page_icon="🎮",
        layout="wide"
    )
    
    st.title("🎮 游戏信息自动填写工具")
    st.markdown("---")
    
    # 初始化工具
    if 'tool' not in st.session_state:
        with st.spinner("正在加载数据..."):
            st.session_state.tool = GameAutoFillTool()
    
    tool = st.session_state.tool
    
    # 侧边栏 - 工具信息
    with st.sidebar:
        st.header("📊 数据统计")
        st.metric("字典记录数", len(tool.title_dict))
        if tool.category_df is not None:
            st.metric("分类合集记录数", len(tool.category_df))
        
        st.header("🔧 功能说明")
        st.markdown("""
        **支持的功能：**
        - 🔍 智能匹配Title ID
        - 📝 自动生成游戏介绍
        - 🖼️ 自动生成图片链接
        - 📋 批量处理
        - 💾 导出Excel文件
        """)
    
    # 主界面选项卡
    tab1, tab2, tab3 = st.tabs(["🎯 单个填写", "📋 批量处理", "📚 字典管理"])
    
    with tab1:
        st.header("单个游戏信息填写")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("输入信息")
            game_name = st.text_input("🎮 游戏中文名称", placeholder="请输入游戏中文名")
            game_link = st.text_input("🔗 游戏链接", placeholder="请输入游戏下载链接")
            game_category = st.selectbox(
                "📂 游戏分类",
                ["动作冒险", "角色扮演", "策略", "体育竞技", "模拟经营", "休闲益智", "多人游戏", "其他"]
            )
            
            if st.button("🚀 自动填写", type="primary"):
                if game_name:
                    with st.spinner("正在处理..."):
                        result = tool.auto_fill_game_info(game_name, game_link, game_category)
                        st.session_state.current_result = result
                else:
                    st.error("请输入游戏中文名称")
        
        with col2:
            st.subheader("填写结果")
            if 'current_result' in st.session_state:
                result = st.session_state.current_result
                
                # 显示匹配信息
                if "精确匹配" in result["匹配信息"]:
                    st.success(f"✅ {result['匹配信息']}")
                elif "模糊匹配" in result["匹配信息"]:
                    st.warning(f"⚠️ {result['匹配信息']}")
                elif "从链接提取" in result["匹配信息"]:
                    st.info(f"ℹ️ {result['匹配信息']}")
                else:
                    st.error(f"❌ {result['匹配信息']}")
                
                # 显示结果
                st.text_input("Title ID", value=result.get("Title ID", ""), disabled=True)
                st.text_input("英文名", value=result.get("英文名", ""), disabled=True)
                st.text_input("发布日期", value=result.get("发布日期", ""), disabled=True)
                st.text_input("地区", value=result.get("地区", ""), disabled=True)
                st.text_area("游戏介绍", value=result.get("游戏介绍", ""), height=150, disabled=True)
                
                # 图片链接
                if result.get("fengmian"):
                    st.text_input("封面图", value=result["fengmian"], disabled=True)
                    st.text_input("截图1", value=result["jietu1"], disabled=True)
                    st.text_input("截图2", value=result["jietu2"], disabled=True)
                    st.text_input("截图3", value=result["jietu3"], disabled=True)
                
                # 导出单个结果
                if st.button("📥 导出为CSV"):
                    df = pd.DataFrame([result])
                    csv = df.to_csv(index=False, encoding='utf-8-sig')
                    st.download_button(
                        label="下载CSV文件",
                        data=csv,
                        file_name=f"{game_name}_游戏信息.csv",
                        mime="text/csv"
                    )
    
    with tab2:
        st.header("批量处理游戏信息")
        
        st.markdown("""
        **使用说明：**
        1. 准备一个CSV文件，包含以下列：`游戏中文名称`, `游戏链接`, `游戏分类`（可选）
        2. 上传文件进行批量处理
        3. 下载处理结果
        """)
        
        # 文件上传
        uploaded_file = st.file_uploader(
            "选择CSV文件",
            type=['csv'],
            help="请上传包含游戏信息的CSV文件"
        )
        
        if uploaded_file is not None:
            try:
                # 读取上传的文件
                input_df = pd.read_csv(uploaded_file)
                st.subheader("📋 输入数据预览")
                st.dataframe(input_df.head())
                
                if st.button("🚀 开始批量处理", type="primary"):
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    results = []
                    
                    total_rows = len(input_df)
                    for i, row in input_df.iterrows():
                        game_name = row.get("游戏中文名称", "")
                        game_link = row.get("游戏链接", "")
                        game_category = row.get("游戏分类", "动作冒险")
                        
                        if game_name:
                            result = tool.auto_fill_game_info(game_name, game_link, game_category)
                            results.append(result)
                            
                            # 更新进度
                            progress = (i + 1) / total_rows
                            progress_bar.progress(progress)
                            status_text.text(f"正在处理: {game_name} ({i+1}/{total_rows})")
                    
                    # 显示结果
                    if results:
                        st.success(f"✅ 批量处理完成！共处理 {len(results)} 个游戏")
                        result_df = pd.DataFrame(results)
                        
                        st.subheader("📊 处理结果预览")
                        st.dataframe(result_df)
                        
                        # 导出结果
                        csv = result_df.to_csv(index=False, encoding='utf-8-sig')
                        st.download_button(
                            label="📥 下载处理结果",
                            data=csv,
                            file_name="批量处理结果.csv",
                            mime="text/csv"
                        )
                        
                        # 统计信息
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            exact_matches = sum(1 for r in results if "精确匹配" in r["匹配信息"])
                            st.metric("精确匹配", exact_matches)
                        with col2:
                            fuzzy_matches = sum(1 for r in results if "模糊匹配" in r["匹配信息"])
                            st.metric("模糊匹配", fuzzy_matches)
                        with col3:
                            link_extracts = sum(1 for r in results if "从链接提取" in r["匹配信息"])
                            st.metric("链接提取", link_extracts)
                        with col4:
                            no_matches = sum(1 for r in results if "未找到匹配" in r["匹配信息"])
                            st.metric("未匹配", no_matches)
                    
            except Exception as e:
                st.error(f"处理文件时出错: {e}")
    
    with tab3:
        st.header("字典管理")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("📚 当前字典统计")
            if tool.title_dict:
                st.metric("字典条目数", len(tool.title_dict))
                
                # 显示部分字典内容
                dict_df = pd.DataFrame(
                    list(tool.title_dict.items())[:10],
                    columns=["游戏名称", "Title ID"]
                )
                st.dataframe(dict_df)
                
                if len(tool.title_dict) > 10:
                    st.info(f"显示前10条，共{len(tool.title_dict)}条记录")
        
        with col2:
            st.subheader("🔍 字典搜索")
            search_term = st.text_input("搜索游戏名称")
            if search_term:
                matches = [name for name in tool.title_dict.keys() if search_term.lower() in name.lower()]
                if matches:
                    for match in matches[:10]:
                        st.text(f"{match} → {tool.title_dict[match]}")
                    if len(matches) > 10:
                        st.info(f"找到{len(matches)}个匹配项，显示前10个")
                else:
                    st.warning("未找到匹配的游戏")
        
        # 导出字典
        if st.button("📥 导出字典为CSV"):
            dict_df = pd.DataFrame(
                list(tool.title_dict.items()),
                columns=["游戏名称", "Title ID"]
            )
            csv = dict_df.to_csv(index=False, encoding='utf-8-sig')
            st.download_button(
                label="下载字典文件",
                data=csv,
                file_name="游戏字典.csv",
                mime="text/csv"
            )

if __name__ == "__main__":
    main()

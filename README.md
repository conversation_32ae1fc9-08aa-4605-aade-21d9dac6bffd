# 🎮 游戏信息自动填写工具

这是一个基于您的 `title.xlsx` 和 `分类合集.xlsx` 数据创建的智能游戏信息填写工具。只需输入游戏中文名和链接，即可自动匹配并填写完整的游戏信息。

## ✨ 功能特点

- 🔍 **智能匹配**: 支持精确匹配、模糊匹配和从链接提取Title ID
- 📝 **自动生成**: 自动生成游戏介绍和图片链接
- 📋 **批量处理**: 支持CSV文件批量处理
- 🌐 **Web界面**: 提供友好的Web操作界面
- 💻 **命令行**: 支持命令行快速操作
- 📊 **数据统计**: 显示匹配成功率和处理结果

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install pandas streamlit
```

### 2. 使用方法

#### 方法一：Web界面（推荐）

```bash
streamlit run web_interface.py
```

然后在浏览器中打开显示的地址（通常是 http://localhost:8501）

#### 方法二：命令行

```bash
# 单个游戏处理
python3 cli_tool.py --name "游戏中文名" --link "游戏链接" --category "游戏分类"

# 示例
python3 cli_tool.py --name "动漫美少女拼图" --link "https://example.com/game" --category "动作冒险"
```

#### 方法三：Python脚本

```python
from game_auto_fill_tool import GameAutoFillTool

# 初始化工具
tool = GameAutoFillTool()

# 填写单个游戏信息
result = tool.auto_fill_game_info(
    game_name="游戏中文名",
    game_link="游戏链接",
    game_category="动作冒险"
)

print(result)
```

## 📋 输入格式

### 单个填写
- **游戏中文名称**: 必填，用于匹配字典中的记录
- **游戏链接**: 可选，如果无法匹配到Title ID，会尝试从链接中提取
- **游戏分类**: 可选，默认为"动作冒险"

### 批量处理
准备一个CSV文件，包含以下列：

```csv
游戏中文名称,游戏链接,游戏分类
塞尔达传说,https://example.com/010028600EBDA000,动作冒险
马里奥奥德赛,https://example.com/0100000000010000,动作冒险
动物森友会,https://example.com/01006F8002326000,模拟经营
```

## 📊 输出格式

工具会自动填写以下信息：

| 字段 | 说明 | 来源 |
|------|------|------|
| gameid | 自动递增ID | 自动生成 |
| Title ID | 游戏的唯一标识符 | 字典匹配/链接提取 |
| 游戏中文名称 | 游戏中文名 | 用户输入 |
| 英文名 | 游戏英文名 | 字典匹配 |
| 发布日期 | 游戏发布日期 | 字典匹配 |
| 地区 | 游戏地区 | 字典匹配 |
| 游戏链接 | 下载链接 | 用户输入 |
| 游戏介绍 | AI生成的游戏介绍 | 自动生成 |
| fengmian | 封面图链接 | 自动生成 |
| jietu1-3 | 游戏截图链接 | 自动生成 |
| 游戏分类 | 游戏分类 | 用户输入/字典匹配 |

## 🔧 匹配机制

1. **精确匹配**: 直接在字典中找到完全相同的游戏名称
2. **模糊匹配**: 使用相似度算法找到最相似的游戏名称（阈值≥0.6）
3. **链接提取**: 从游戏链接中提取Title ID（支持16位和12位十六进制格式）
4. **未匹配**: 如果以上方法都失败，会标记为"未找到匹配"

## 📁 文件说明

- `game_auto_fill_tool.py`: 核心工具类
- `web_interface.py`: Streamlit Web界面
- `cli_tool.py`: 命令行工具
- `example_input.csv`: 批量处理示例文件
- `category_analysis.csv`: 从分类合集.xlsx提取的数据
- `title_converted.csv`: 从title.xlsx提取的数据（如果成功转换）

## 🎯 使用场景

1. **日常填写**: 只需输入游戏中文名，快速获取完整信息
2. **批量导入**: 处理大量游戏数据，提高工作效率
3. **数据验证**: 检查现有数据的完整性和准确性
4. **内容生成**: 自动生成标准化的游戏介绍文本

## ⚠️ 注意事项

1. 工具基于现有的分类合集数据构建字典，匹配效果取决于数据质量
2. 模糊匹配可能不够准确，建议人工核查重要数据
3. 自动生成的游戏介绍是基于模板的，可能需要人工调整
4. 图片链接是根据Title ID生成的，实际可用性需要验证

## 🔄 更新字典

如果您有新的title.xlsx文件，只需：

1. 将新文件放在工具目录下
2. 删除旧的 `title_converted.csv` 文件
3. 重新运行工具，它会自动重新构建字典

## 📈 统计信息

工具会显示以下统计信息：
- 字典记录总数
- 精确匹配数量
- 模糊匹配数量
- 链接提取数量
- 未匹配数量

这些信息可以帮助您评估数据质量和工具效果。

## 🤝 技术支持

如果您在使用过程中遇到问题，请检查：

1. 输入数据格式是否正确
2. 依赖包是否已安装
3. 文件路径是否正确
4. 数据文件是否存在且可读

---

**祝您使用愉快！** 🎮✨
